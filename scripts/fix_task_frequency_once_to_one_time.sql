-- Fix Task Frequency: Update ONCE to ONE_TIME
-- This script fixes the inconsistency where tasks created via admin API with frequency "ONCE"
-- need to be updated to "ONE_TIME" to match the internal model constants.

-- Start transaction
BEGIN;

-- Check current state
SELECT 
    'Before Update' as status,
    frequency,
    COUNT(*) as count
FROM activity_tasks 
WHERE frequency IN ('ONCE', 'ONE_TIME')
GROUP BY frequency;

-- Update tasks with frequency 'ONCE' to 'ONE_TIME'
UPDATE activity_tasks 
SET 
    frequency = 'ONE_TIME',
    updated_at = CURRENT_TIMESTAMP
WHERE frequency = 'ONCE';

-- Get the number of affected rows
SELECT 
    'Updated Rows' as status,
    ROW_COUNT() as affected_rows;

-- Check final state
SELECT 
    'After Update' as status,
    frequency,
    COUNT(*) as count
FROM activity_tasks 
WHERE frequency IN ('ONCE', 'ONE_TIME')
GROUP BY frequency;

-- Show all tasks that were updated (for verification)
SELECT 
    id,
    name,
    frequency,
    category_id,
    updated_at
FROM activity_tasks 
WHERE frequency = 'ONE_TIME'
ORDER BY updated_at DESC;

-- Commit the transaction
COMMIT;

-- Verification: Ensure no tasks have 'ONCE' frequency anymore
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN 'SUCCESS: No tasks with ONCE frequency found'
        ELSE CONCAT('ERROR: ', COUNT(*), ' tasks still have ONCE frequency')
    END as verification_result
FROM activity_tasks 
WHERE frequency = 'ONCE';
