package main

import (
	"context"
	"flag"
	"fmt"
	"log"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/initializer"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"go.uber.org/zap"
)

func main() {
	var configPath string
	var dryRun bool
	flag.StringVar(&configPath, "config", "config.yaml", "Path to config file")
	flag.BoolVar(&dryRun, "dry-run", false, "Show what would be updated without making changes")
	flag.Parse()

	// Initialize configuration
	global.GVA_VP = initializer.Viper(configPath)
	global.GVA_LOG = initializer.Zap()
	global.GVA_DB = initializer.GormPgSql()

	if global.GVA_DB == nil {
		log.Fatal("Failed to initialize database connection")
	}

	ctx := context.Background()

	fmt.Println("🔍 Checking for tasks with frequency 'ONCE'...")

	// Find tasks with frequency 'ONCE'
	var tasksWithOnce []model.ActivityTask
	if err := global.GVA_DB.WithContext(ctx).
		Where("frequency = ?", "ONCE").
		Find(&tasksWithOnce).Error; err != nil {
		log.Fatalf("Failed to query tasks: %v", err)
	}

	if len(tasksWithOnce) == 0 {
		fmt.Println("✅ No tasks found with frequency 'ONCE'. Nothing to fix.")
		return
	}

	fmt.Printf("📋 Found %d tasks with frequency 'ONCE':\n", len(tasksWithOnce))
	for _, task := range tasksWithOnce {
		fmt.Printf("  - ID: %s, Name: %s, Category: %d\n", task.ID.String(), task.Name, task.CategoryID)
	}

	if dryRun {
		fmt.Println("\n🔍 DRY RUN MODE: No changes will be made.")
		fmt.Printf("Would update %d tasks from frequency 'ONCE' to 'ONE_TIME'\n", len(tasksWithOnce))
		return
	}

	fmt.Printf("\n🔧 Updating %d tasks from frequency 'ONCE' to 'ONE_TIME'...\n", len(tasksWithOnce))

	// Start transaction
	tx := global.GVA_DB.Begin()
	if tx.Error != nil {
		log.Fatalf("Failed to start transaction: %v", tx.Error)
	}

	// Update tasks
	result := tx.WithContext(ctx).
		Model(&model.ActivityTask{}).
		Where("frequency = ?", "ONCE").
		Update("frequency", model.FrequencyOneTime)

	if result.Error != nil {
		tx.Rollback()
		log.Fatalf("Failed to update tasks: %v", result.Error)
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		log.Fatalf("Failed to commit transaction: %v", err)
	}

	global.GVA_LOG.Info("Successfully updated task frequencies",
		zap.Int64("affected_rows", result.RowsAffected))

	fmt.Printf("✅ Successfully updated %d tasks from 'ONCE' to 'ONE_TIME'\n", result.RowsAffected)

	// Verify the fix
	var remainingOnce int64
	if err := global.GVA_DB.WithContext(ctx).
		Model(&model.ActivityTask{}).
		Where("frequency = ?", "ONCE").
		Count(&remainingOnce).Error; err != nil {
		log.Printf("Warning: Failed to verify fix: %v", err)
	} else if remainingOnce > 0 {
		log.Printf("Warning: %d tasks still have frequency 'ONCE'", remainingOnce)
	} else {
		fmt.Println("✅ Verification passed: No tasks with frequency 'ONCE' remain")
	}

	fmt.Println("🎉 Task frequency fix completed successfully!")
}
